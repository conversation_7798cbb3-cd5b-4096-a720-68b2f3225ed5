/**
 * <PERSON><PERSON><PERSON> diferenças entre cálculos e gera recomendações individuais
 * Compara valores nas planilhas e insere recomendações específicas
 */
function analisarDiferencasEGerarRecomendacoes() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var ui = SpreadsheetApp.getUi();

  try {
    // Obtém referências para todas as abas necessárias
    var calculosSheet = ss.getSheetByName('Cálculos- Individual');
    var analiseSheet = ss.getSheetByName('Análise');
    var referenciaSheet = ss.getSheetByName('Referência');
    var acoesSheet = ss.getSheetByName('Ações - Individual');

    // Carrega dados das planilhas
    var calculosData = calculosSheet.getDataRange().getValues();
    var analiseData = analiseSheet.getDataRange().getValues();
    var referenciaData = referenciaSheet.getDataRange().getValues();

    var recomendacoesParaInserir = [];

    console.log('=== INICIANDO ANÁLISE DE DIFERENÇAS ===');

    // Passo 1: Compara valores E2:E11 vs C2:C11 na planilha Cálculos
    for (var i = 1; i <= 10; i++) { // Linhas 2-11 (índices 1-10)
      var valorE = calculosData[i][4]; // Coluna E (índice 4)
      var valorC = calculosData[i][2]; // Coluna C (índice 2)
      var subPilar = calculosData[i][1]; // Coluna B (índice 1) - Sub Pilar

      console.log('Linha ' + (i + 1) + ': E=' + valorE + ', C=' + valorC + ', Sub Pilar="' + subPilar + '"');

      // Passo 2: Se valores são diferentes, processa essa linha
      if (valorE !== valorC) {
        console.log('  ✓ Diferença encontrada! Processando Sub Pilar: "' + subPilar + '"');

        // Passo 3: Busca o Sub Pilar na planilha Análise (coluna C2:C51 - ajustado para nova estrutura)
        var linhaAnalise = buscarSubPilarNaAnalise(analiseData, subPilar);

        if (linhaAnalise !== -1) {
          console.log('  ✓ Sub Pilar encontrado na linha ' + (linhaAnalise + 1) + ' da Análise');

          // Passo 4: Usa a pontuação (valorE) para buscar classificação na Referência
          var classificacao = buscarClassificacaoPorPontuacao(referenciaData, valorE);

          if (classificacao) {
            console.log('  ✓ Classificação encontrada: "' + classificacao + '" para pontuação ' + valorE);

            // Passo 5: Busca a recomendação correspondente na linha já encontrada
            var recomendacao = buscarRecomendacaoNaLinhaAnalise(analiseData, linhaAnalise, classificacao);

            if (recomendacao) {
              console.log('  ✓ Recomendação encontrada: "' + recomendacao.substring(0, 50) + '..."');
              recomendacoesParaInserir.push([recomendacao]);
            } else {
              console.log('  ✗ Recomendação não encontrada para: ' + subPilar + ' + ' + classificacao);
            }
          } else {
            console.log('  ✗ Classificação não encontrada para pontuação: ' + valorE);
          }
        } else {
          console.log('  ✗ Sub Pilar não encontrado na Análise: "' + subPilar + '"');
        }
      }
    }

    // Passo 6: Limpa e insere recomendações na planilha Ações - Individual
    if (recomendacoesParaInserir.length > 0) {
      // Limpa dados existentes (mantém cabeçalho se houver)
      var lastRow = acoesSheet.getLastRow();
      if (lastRow > 1) {
        acoesSheet.getRange(2, 1, lastRow - 1, acoesSheet.getLastColumn()).clearContent();
      }

      // Insere as novas recomendações a partir de A2
      acoesSheet.getRange(2, 1, recomendacoesParaInserir.length, 1).setValues(recomendacoesParaInserir);

      ui.alert('Análise Concluída',
               'Análise de diferenças concluída!\nRecomendações inseridas: ' + recomendacoesParaInserir.length,
               ui.ButtonSet.OK);
    } else {
      ui.alert('Nenhuma Diferença',
               'Não foram encontradas diferenças entre as colunas E e C, ou não foi possível gerar recomendações.',
               ui.ButtonSet.OK);
    }

  } catch (error) {
    console.log('Erro durante execução:', error);
    ui.alert('Erro', 'Erro durante a execução: ' + error.message, ui.ButtonSet.OK);
  }
}

/**
 * Busca um Sub Pilar específico na planilha Análise (coluna C2:C51)
 * @param {Array} analiseData - Dados da planilha Análise
 * @param {string} subPilar - Sub Pilar a ser procurado
 * @return {number} - Índice da linha encontrada ou -1 se não encontrada
 */
function buscarSubPilarNaAnalise(analiseData, subPilar) {
  var subPilarBusca = subPilar ? subPilar.toString().trim() : "";

  if (subPilarBusca === "") {
    return -1;
  }

  // Remove numeração do Sub Pilar para busca mais flexível
  var subPilarLimpo = limparNumeracaoSubPilar(subPilarBusca);

  // Primeira tentativa: busca exata
  for (var i = 1; i < Math.min(51, analiseData.length); i++) {
    if (analiseData[i] && analiseData[i].length > 2) {
      var subPilarAnalise = analiseData[i][2] ? analiseData[i][2].toString().trim() : "";

      if (subPilarAnalise === subPilarBusca) {
        console.log('    → Sub Pilar encontrado (exato) na linha ' + (i + 1) + ': "' + subPilarAnalise + '"');
        return i;
      }
    }
  }

  // Segunda tentativa: busca sem numeração
  for (var i = 1; i < Math.min(51, analiseData.length); i++) {
    if (analiseData[i] && analiseData[i].length > 2) {
      var subPilarAnalise = analiseData[i][2] ? analiseData[i][2].toString().trim() : "";
      var subPilarAnaliseLimpo = limparNumeracaoSubPilar(subPilarAnalise);

      if (subPilarAnaliseLimpo === subPilarLimpo) {
        console.log('    → Sub Pilar encontrado (sem numeração) na linha ' + (i + 1) + ': "' + subPilarAnalise + '"');
        return i;
      }
    }
  }

  // Terceira tentativa: busca por palavras-chave
  for (var i = 1; i < Math.min(51, analiseData.length); i++) {
    if (analiseData[i] && analiseData[i].length > 2) {
      var subPilarAnalise = analiseData[i][2] ? analiseData[i][2].toString().trim() : "";

      if (contemPalavrasChaveSubPilar(subPilarAnalise, subPilarLimpo)) {
        console.log('    → Sub Pilar encontrado (palavras-chave) na linha ' + (i + 1) + ': "' + subPilarAnalise + '"');
        return i;
      }
    }
  }

  console.log('    → Sub Pilar não encontrado: "' + subPilarBusca + '"');
  return -1;
}

/**
 * Busca a classificação correspondente a uma pontuação na planilha Referência
 * @param {Array} referenciaData - Dados da planilha Referência
 * @param {number} pontuacao - Pontuação a ser procurada
 * @return {string|null} - Classificação encontrada ou null
 */
function buscarClassificacaoPorPontuacao(referenciaData, pontuacao) {
  // Busca no intervalo B2:C6 (índices 1-5, colunas 1-2)
  for (var i = 1; i <= 5; i++) {
    if (referenciaData[i] && referenciaData[i].length > 2) {
      var classificacaoRef = referenciaData[i][1]; // Coluna B (índice 1)
      var pontuacaoRef = referenciaData[i][2];     // Coluna C (índice 2)

      // Converte pontuações para números para comparação
      var pontuacaoRefNum = parseFloat(pontuacaoRef);
      var pontuacaoBuscaNum = parseFloat(pontuacao);

      if (!isNaN(pontuacaoRefNum) && !isNaN(pontuacaoBuscaNum) && pontuacaoRefNum === pontuacaoBuscaNum) {
        console.log('    → Classificação encontrada: "' + classificacaoRef + '" para pontuação ' + pontuacao);
        return classificacaoRef;
      }
    }
  }

  console.log('    → Classificação não encontrada para pontuação: ' + pontuacao);
  return null;
}

/**
 * Busca a recomendação na planilha Análise baseada no Sub Pilar e Classificação
 * @param {Array} analiseData - Dados da planilha Análise
 * @param {string} subPilar - Sub Pilar a ser procurado
 * @param {string} classificacao - Classificação a ser procurada
 * @return {string|null} - Recomendação encontrada ou null
 */
function buscarRecomendacaoNaLinhaAnalise(analiseData, linhaAnalise, classificacao) {
  var classificacaoBusca = classificacao ? classificacao.toString().trim() : "";

  console.log('    → Buscando recomendação na linha ' + (linhaAnalise + 1) + ' para classificação: "' + classificacaoBusca + '"');

  // Busca a partir da linha encontrada, procurando pela classificação
  for (var i = linhaAnalise; i < analiseData.length; i++) {
    if (analiseData[i] && analiseData[i].length > 6) {
      var classificacaoAnalise = analiseData[i][4] ? analiseData[i][4].toString().trim() : ""; // Coluna E (índice 4)

      console.log('      Linha ' + (i + 1) + ': Classificação = "' + classificacaoAnalise + '"');

      if (classificacaoAnalise === classificacaoBusca) {
        var recomendacao = analiseData[i][6] ? analiseData[i][6].toString().trim() : ""; // Coluna G (índice 6)

        if (recomendacao !== "") {
          console.log('    → Recomendação encontrada na linha ' + (i + 1) + ': "' + recomendacao.substring(0, 100) + '..."');
          return recomendacao;
        }
      }

      // Para de buscar se encontrar uma nova pergunta (nova seção)
      var perguntaAtual = analiseData[i][2] ? analiseData[i][2].toString().trim() : "";
      if (i > linhaAnalise && perguntaAtual !== "" && perguntaAtual !== analiseData[linhaAnalise][2]) {
        console.log('    → Parou busca: nova seção encontrada na linha ' + (i + 1));
        break;
      }
    }
  }

  console.log('    → Recomendação não encontrada para classificação: "' + classificacaoBusca + '"');
  return null;
}

/**
 * Remove numeração do início do Sub Pilar para facilitar correspondência
 * @param {string} subPilar - Sub Pilar com possível numeração
 * @return {string} - Sub Pilar sem numeração
 */
function limparNumeracaoSubPilar(subPilar) {
  if (!subPilar) return "";

  // Remove padrões como "1. ", "10. ", etc.
  return subPilar.replace(/^\d+\.\s*/, "").trim();
}

/**
 * Verifica se dois Sub Pilares são similares usando palavras-chave
 * @param {string} subPilar1 - Primeiro Sub Pilar
 * @param {string} subPilar2 - Segundo Sub Pilar
 * @return {boolean} - true se são similares
 */
function contemPalavrasChaveSubPilar(subPilar1, subPilar2) {
  if (!subPilar1 || !subPilar2) return false;

  var texto1 = subPilar1.toLowerCase();
  var texto2 = subPilar2.toLowerCase();

  // Palavras-chave importantes para identificar Sub Pilares
  var palavrasChave = [
    'estratégia', 'segurança', 'identidades',
    'governança', 'controle', 'acessos',
    'cobertura', 'ambientes',
    'responsabilidade', 'operacional',
    'autenticação', 'experiência', 'usuário',
    'privilegiados', 'pam',
    'integração', 'ecossistema', 'tecnológico',
    'ciclo', 'vida',
    'revisões', 'auditoria',
    'análise', 'risco', 'inteligência'
  ];

  var coincidencias = 0;
  palavrasChave.forEach(function(palavra) {
    if (texto1.includes(palavra) && texto2.includes(palavra)) {
      coincidencias++;
    }
  });

  // Retorna true se há pelo menos 2 palavras-chave em comum
  return coincidencias >= 2;
}

/**
 * Função de debug para verificar os Sub Pilares na aba 'Análise'
 */
function debugSubPilaresAnalise() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var analiseSheet = ss.getSheetByName('Análise');
  var analiseData = analiseSheet.getDataRange().getValues();

  console.log('=== DEBUG: Sub Pilares na aba Análise ===');
  console.log('Coluna C (índice 2) - Sub Pilares:');

  var subPilaresUnicos = [];
  for (var i = 1; i < analiseData.length; i++) {
    if (analiseData[i] && analiseData[i][2]) {
      var subPilar = analiseData[i][2].toString().trim();
      if (subPilar !== "" && subPilaresUnicos.indexOf(subPilar) === -1) {
        subPilaresUnicos.push(subPilar);
      }
    }
  }

  subPilaresUnicos.forEach(function(subPilar, index) {
    console.log((index + 1) + '. "' + subPilar + '"');
  });

  var ui = SpreadsheetApp.getUi();
  ui.alert('Debug Sub Pilares', 'Verifique o console para ver os Sub Pilares da aba Análise.', ui.ButtonSet.OK);
}

// Fim do arquivo - linha de fechamento para garantir sintaxe correta
