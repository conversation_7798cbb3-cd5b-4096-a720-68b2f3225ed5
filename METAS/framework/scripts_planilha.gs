/**
 * Analisa dados específicos baseados em email e data fornecidos pelo usuário
 * Limpa a planilha 'Automatização da análise' e inclui apenas os dados filtrados
 */
function analisarPorEmailEData() {
  var ui = SpreadsheetApp.getUi();

  // Solicita email e data ao usuário
  var emailResponse = ui.prompt('Filtro por Email', 'Digite o email que deseja analisar:', ui.ButtonSet.OK_CANCEL);
  if (emailResponse.getSelectedButton() !== ui.Button.OK) {
    return; // Usuário cancelou
  }
  var emailFiltro = emailResponse.getResponseText().trim();

  var dataResponse = ui.prompt('Filtro por Data', 'Digite a data que deseja analisar (formato: DD/MM/AAAA):', ui.ButtonSet.OK_CANCEL);
  if (dataResponse.getSelectedButton() !== ui.Button.OK) {
    return; // Usuário cancelou
  }
  var dataFiltro = dataResponse.getResponseText().trim();

  // Valida se os campos foram preenchidos
  if (!emailFiltro || !dataFiltro) {
    ui.alert('Erro', 'Email e data são obrigatórios!', ui.ButtonSet.OK);
    return;
  }

  // Executa a análise filtrada
  executarAnaliseFiltrада(emailFiltro, dataFiltro);
}

/**
 * Executa a análise filtrada por email e data específicos
 * @param {string} emailFiltro - Email para filtrar os dados
 * @param {string} dataFiltro - Data para filtrar os dados (formato DD/MM/AAAA)
 */
function executarAnaliseFiltrада(emailFiltro, dataFiltro) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var ui = SpreadsheetApp.getUi();

  // Obtém referências para as abas da planilha
  var respostasSheet = ss.getSheetByName('Respostas');
  var analiseSheet = ss.getSheetByName('Análise');
  var automatizacaoSheet = ss.getSheetByName('Automatização da análise');

  // Carrega todos os dados das abas em arrays
  var respostasData = respostasSheet.getDataRange().getValues();
  var analiseData = analiseSheet.getDataRange().getValues();

  // Obtém a linha de cabeçalho com as perguntas (linha 2, índice 1)
  var lPerguntas = respostasData[1];
  var rowsToAdd = []; // Array para armazenar linhas filtradas

  // Corrige a estrutura dos dados de análise
  arrumarEstruturaVetores(analiseData);

  // Converte a data do filtro para objeto Date para comparação
  var dataFiltroObj = converterStringParaData(dataFiltro);
  if (!dataFiltroObj) {
    ui.alert('Erro', 'Formato de data inválido! Use DD/MM/AAAA', ui.ButtonSet.OK);
    return;
  }

  var dadosEncontrados = false;

  // Percorre todas as respostas (começando da linha 3, índice 2)
  for (var i = 2; i < respostasData.length; i++) {
    var email = respostasData[i][1]; // Email do respondente (coluna B)
    var dataResposta = respostasData[i][0]; // Data da resposta (coluna A)

    // Verifica se o email corresponde ao filtro
    if (email.trim().toLowerCase() !== emailFiltro.toLowerCase()) {
      continue; // Pula para a próxima linha se email não corresponde
    }

    // Verifica se a data corresponde ao filtro
    var dataRespostaObj = new Date(dataResposta);
    if (!compararDatas(dataRespostaObj, dataFiltroObj)) {
      continue; // Pula para a próxima linha se data não corresponde
    }

    dadosEncontrados = true;

    // Percorre todas as perguntas (excluindo as últimas 4 colunas)
    for (var j = 2; j < (respostasData[i].length - 4); j++) {
      var pergunta = lPerguntas[j];
      var respostaCliente = respostasData[i][j];

      // Busca a linha correspondente na aba de análise
      var locLinha = achaLinha(analiseData, pergunta, respostaCliente);
      var maturidade, recomendacao, planoAcao;

      // Se não encontrou correspondência, marca como N/A
      if (locLinha === "N/A") {
        maturidade = "N/A";
        recomendacao = "N/A";
        planoAcao = "N/A";
      } else {
        // Extrai os dados da linha encontrada
        maturidade = analiseData[locLinha][3];
        recomendacao = analiseData[locLinha][4];
        planoAcao = analiseData[locLinha][5];
      }

      // Cria uma nova linha com todos os dados
      var novaLinha = [email, pergunta, respostaCliente, maturidade, recomendacao, planoAcao];
      rowsToAdd.push(novaLinha);
    }
  }

  // Limpa a planilha de automatização (mantém apenas o cabeçalho)
  limparPlanilhaAutomatizacao(automatizacaoSheet);

  // Se encontrou dados para o filtro especificado
  if (dadosEncontrados && rowsToAdd.length > 0) {
    // Adiciona as linhas filtradas na aba de automatização
    automatizacaoSheet.getRange(2, 1, rowsToAdd.length, 6).setValues(rowsToAdd);
    ui.alert('Análise Concluída',
             `Análise filtrada concluída com sucesso!\nEmail: ${emailFiltro}\nData: ${dataFiltro}\nLinhas processadas: ${rowsToAdd.length}`,
             ui.ButtonSet.OK);
  } else {
    ui.alert('Nenhum Dado Encontrado',
             `Não foram encontrados dados para:\nEmail: ${emailFiltro}\nData: ${dataFiltro}`,
             ui.ButtonSet.OK);
  }
}

/**
 * Limpa todos os dados da planilha 'Automatização da análise', mantendo apenas o cabeçalho
 * @param {Sheet} automatizacaoSheet - Referência para a aba de automatização
 */
function limparPlanilhaAutomatizacao(automatizacaoSheet) {
  var lastRow = automatizacaoSheet.getLastRow();
  var lastCol = automatizacaoSheet.getLastColumn();

  // Se há dados além do cabeçalho (linha 1), limpa tudo a partir da linha 2
  if (lastRow > 1) {
    automatizacaoSheet.getRange(2, 1, lastRow - 1, lastCol).clearContent();
  }
}

/**
 * Converte uma string de data no formato DD/MM/AAAA para objeto Date
 * @param {string} dataString - String da data no formato DD/MM/AAAA
 * @return {Date|null} - Objeto Date ou null se formato inválido
 */
function converterStringParaData(dataString) {
  try {
    // Divide a string da data em partes
    var partes = dataString.split('/');
    if (partes.length !== 3) {
      return null;
    }

    var dia = parseInt(partes[0]);
    var mes = parseInt(partes[1]) - 1; // Mês em JavaScript é 0-indexado
    var ano = parseInt(partes[2]);

    // Valida se os valores são números válidos
    if (isNaN(dia) || isNaN(mes) || isNaN(ano)) {
      return null;
    }

    // Cria e retorna o objeto Date
    var data = new Date(ano, mes, dia);

    // Verifica se a data criada é válida
    if (data.getDate() !== dia || data.getMonth() !== mes || data.getFullYear() !== ano) {
      return null;
    }

    return data;
  } catch (error) {
    return null;
  }
}

/**
 * Compara duas datas para verificar se são do mesmo dia
 * @param {Date} data1 - Primeira data para comparação
 * @param {Date} data2 - Segunda data para comparação
 * @return {boolean} - true se as datas são do mesmo dia, false caso contrário
 */
function compararDatas(data1, data2) {
  if (!data1 || !data2) {
    return false;
  }

  return data1.getDate() === data2.getDate() &&
         data1.getMonth() === data2.getMonth() &&
         data1.getFullYear() === data2.getFullYear();
}

/**
 * Função alternativa que permite especificar email e data diretamente como parâmetros
 * Útil para testes ou chamadas programáticas
 * @param {string} email - Email para filtrar
 * @param {string} data - Data para filtrar (formato DD/MM/AAAA)
 */
function analisarPorEmailEDataDireto(email, data) {
  if (!email || !data) {
    var ui = SpreadsheetApp.getUi();
    ui.alert('Erro', 'Email e data são obrigatórios!', ui.ButtonSet.OK);
    return;
  }

  executarAnaliseFiltrада(email, data);
}

/**
 * Busca uma linha específica no array de análise baseada na pergunta e resposta
 * @param {Array} array - Array bidimensional com os dados de análise
 * @param {string} perg - Pergunta a ser procurada
 * @param {string} resp - Resposta a ser procurada
 * @return {number|string} - Índice da linha encontrada ou "N/A" se não encontrada
 */
function achaLinha(array, perg, resp) {
  // Percorre o array começando da linha 2 (índice 1)
  for (let y = 1; y < array.length; y++) {
    // Compara pergunta e resposta removendo espaços em branco
    if (array[y][1].trim() === perg.trim() && array[y][2].trim() === resp.trim()) {
      return y; // Retorna o índice da linha encontrada
    } else if (resp === "Não se aplica") {
      return "N/A"; // Retorna N/A para respostas não aplicáveis
    }
  }
  return "N/A"; // Retorna N/A se não encontrou correspondência
}

/**
 * Corrige a estrutura dos dados de análise preenchendo campos vazios
 * com valores das linhas anteriores (modalidade e pergunta)
 * @param {Array} analiseData - Array bidimensional com os dados de análise
 */
function arrumarEstruturaVetores(analiseData) {
  var modalidadeP, perguntaP; // Variáveis para armazenar valores anteriores

  // Percorre todas as linhas dos dados de análise
  for (var cl = 1; cl < analiseData.length; cl++) {
    var clData = analiseData[cl];

    // Se ambos modalidade e pergunta estão preenchidos, atualiza as variáveis
    if (clData[1] !== "" && clData[0] !== "") {
      modalidadeP = clData[0];
      perguntaP = clData[1];
    }
    // Se modalidade está vazia mas pergunta está preenchida
    else if (clData[0] === "" && clData[1] !== "") {
      perguntaP = clData[1];
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
    // Se pergunta está vazia
    else if (clData[1] === "") {
      clData[1] = perguntaP;   // Preenche pergunta com valor anterior
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
  }
}