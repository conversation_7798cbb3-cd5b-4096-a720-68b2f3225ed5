/**
 * Copia a última linha de fórmulas da planilha e ajusta as referências das células
 * para criar uma nova linha com fórmulas atualizadas
 */
function copiarUltimaLinha() {
  // Abre a planilha específica pelo ID
  var ss = SpreadsheetApp.openById('1omekRbbZBbE0w5OcERRbkNCAb1-1chyvQGbyQEGBbQk');
  var sheet = ss.getSheetByName('Fórmulas - somente atualizar');

  // Obtém informações sobre a última linha com dados
  var lastRow = sheet.getLastRow();
  var lastCol = sheet.getLastColumn();
  var lastRowFormulas = sheet.getRange(lastRow, 1, 1, lastCol).getFormulas();

  // Percorre todas as colunas e linhas das fórmulas obtidas
  for (var col = 0; col < lastRowFormulas[0].length; col++) {
    for (var row = 0; row < lastRowFormulas.length; row++) {
      var formula = lastRowFormulas[row][col];

      // Se a célula contém uma fórmula, ajusta as referências
      if (formula !== "") {
        // Usa regex para encontrar referências de células (ex: A1, B2, etc.)
        var adjustedFormula = formula.replace(/\$?([A-Z]+)(\d+)/g, function(match, p1, p2) {
          // Mantém referências absolutas ($A$1) ou referências especiais
          if (match.startsWith("$") || p1 === "0") {
            return match;
          } else {
            // Incrementa o número da linha em 1 para a nova linha
            var newRowIndex = parseInt(p2) + 1;
            return p1 + newRowIndex;
          }
        });
        lastRowFormulas[row][col] = adjustedFormula;
      }
    }
  }

  // Insere as fórmulas ajustadas na próxima linha
  sheet.getRange(lastRow + 1, 1, 1, lastCol).setFormulas(lastRowFormulas);
}

/**
 * Automatiza a análise das respostas do formulário, cruzando dados entre as abas
 * 'Respostas', 'Análise' e 'Automatização da análise'
 */
function automatizarAnalise() {
  // Obtém referências para as abas da planilha ativa
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var respostasSheet = ss.getSheetByName('Respostas');
  var analiseSheet = ss.getSheetByName('Análise');
  var automatizacaoSheet = ss.getSheetByName('Automatização da análise');

  // Carrega todos os dados das abas em arrays
  var respostasData = respostasSheet.getDataRange().getValues();
  var analiseData = analiseSheet.getDataRange().getValues();
  var automatizacaoData = automatizacaoSheet.getDataRange().getValues();

  // Obtém a linha de cabeçalho com as perguntas (linha 2, índice 1)
  var lPerguntas = respostasData[1];
  var rowsToAdd = []; // Array para armazenar novas linhas a serem adicionadas

  // Corrige a estrutura dos dados de análise
  arrumarEstruturaVetores(analiseData);

  // Percorre todas as respostas (começando da linha 3, índice 2)
  for (var i = 2; i < respostasData.length; i++) {
    var email = respostasData[i][1]; // Email do respondente

    // Percorre todas as perguntas (excluindo as últimas 4 colunas)
    for (var j = 2; j < (respostasData[i].length - 4); j++) {
      var pergunta = lPerguntas[j];
      var respostaCliente = respostasData[i][j];

      // Busca a linha correspondente na aba de análise
      var locLinha = achaLinha(analiseData, pergunta, respostaCliente);
      var maturidade, recomendacao, planoAcao;

      // Se não encontrou correspondência, marca como N/A
      if (locLinha === "N/A") {
        maturidade = "N/A";
        recomendacao = "N/A";
        planoAcao = "N/A";
      } else {
        // Extrai os dados da linha encontrada
        maturidade = analiseData[locLinha][3];
        recomendacao = analiseData[locLinha][4];
        planoAcao = analiseData[locLinha][5];
      }

      // Cria uma nova linha com todos os dados
      var novaLinha = [email, pergunta, respostaCliente, maturidade, recomendacao, planoAcao];

      // Verifica se a linha já existe na aba de automatização
      var linhaExistente = automatizacaoData.find(function (row) {
        return (
          row[0] === novaLinha[0] &&
          row[1] === novaLinha[1] &&
          row[2] === novaLinha[2] &&
          row[3] === novaLinha[3] &&
          row[4] === novaLinha[4] &&
          row[5] === novaLinha[5]
        );
      });

      // Verifica se a linha já está no array de linhas a serem adicionadas
      var linhaExistenteNoVetor = verificarDuplicatasNoVetor(rowsToAdd, novaLinha);

      // Adiciona a linha apenas se não for duplicata
      if (!linhaExistente && !linhaExistenteNoVetor) {
        rowsToAdd.push(novaLinha);
      }
    }
  }

  // Se há novas linhas para adicionar
  if (rowsToAdd.length > 0) {
    // Adiciona as novas linhas na aba de automatização
    automatizacaoSheet.getRange(automatizacaoSheet.getLastRow() + 1, 1, rowsToAdd.length, 6).setValues(rowsToAdd);
    var ui = SpreadsheetApp.getUi();
    ui.alert('Script Finalizado', 'Transcrição Finalizada com Sucesso, novas linhas adicionadas!', ui.ButtonSet.OK);
  } else {
    // Informa que não há novas respostas para processar
    var ui = SpreadsheetApp.getUi();
    ui.alert('Nenhuma nova resposta', 'Não foi possível acrescentar pois não existem novas respostas ou somente respostas duplicadas!', ui.ButtonSet.OK);
  }
}

/**
 * Busca uma linha específica no array de análise baseada na pergunta e resposta
 * @param {Array} array - Array bidimensional com os dados de análise
 * @param {string} perg - Pergunta a ser procurada
 * @param {string} resp - Resposta a ser procurada
 * @return {number|string} - Índice da linha encontrada ou "N/A" se não encontrada
 */
function achaLinha(array, perg, resp) {
  // Percorre o array começando da linha 2 (índice 1)
  for (let y = 1; y < array.length; y++) {
    // Compara pergunta e resposta removendo espaços em branco
    if (array[y][1].trim() === perg.trim() && array[y][2].trim() === resp.trim()) {
      return y; // Retorna o índice da linha encontrada
    } else if (resp === "Não se aplica") {
      return "N/A"; // Retorna N/A para respostas não aplicáveis
    }
  }
  return "N/A"; // Retorna N/A se não encontrou correspondência
}

/**
 * Verifica se uma linha já existe no array de linhas a serem adicionadas
 * @param {Array} rowsToAdd - Array com as linhas que serão adicionadas
 * @param {Array} novaLinha - Nova linha a ser verificada
 * @return {boolean} - true se a linha já existe, false caso contrário
 */
function verificarDuplicatas(rowsToAdd, novaLinha) {
  // Percorre todas as linhas no array
  for (var i = 0; i < rowsToAdd.length; i++) {
    var linhaExistente = rowsToAdd[i];

    // Compara todos os campos da linha
    if (
      linhaExistente[0] === novaLinha[0] && // Email
      linhaExistente[1] === novaLinha[1] && // Pergunta
      linhaExistente[2] === novaLinha[2] && // Resposta
      linhaExistente[3] === novaLinha[3] && // Maturidade
      linhaExistente[4] === novaLinha[4] && // Recomendação
      linhaExistente[5] === novaLinha[5]    // Plano de Ação
    ) {
      return true; // Linha duplicada encontrada
    }
  }
  return false; // Linha não é duplicata
}

/**
 * Corrige a estrutura dos dados de análise preenchendo campos vazios
 * com valores das linhas anteriores (modalidade e pergunta)
 * @param {Array} analiseData - Array bidimensional com os dados de análise
 */
function arrumarEstruturaVetores(analiseData) {
  var modalidadeP, perguntaP; // Variáveis para armazenar valores anteriores

  // Percorre todas as linhas dos dados de análise
  for (var cl = 1; cl < analiseData.length; cl++) {
    var clData = analiseData[cl];

    // Se ambos modalidade e pergunta estão preenchidos, atualiza as variáveis
    if (clData[1] !== "" && clData[0] !== "") {
      modalidadeP = clData[0];
      perguntaP = clData[1];
    }
    // Se modalidade está vazia mas pergunta está preenchida
    else if (clData[0] === "" && clData[1] !== "") {
      perguntaP = clData[1];
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
    // Se pergunta está vazia
    else if (clData[1] === "") {
      clData[1] = perguntaP;   // Preenche pergunta com valor anterior
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
  }
}

/**
 * Verifica se uma linha já existe no vetor de linhas a serem adicionadas
 * (Função similar a verificarDuplicatas, mas com nome mais específico)
 * @param {Array} rowsToAdd - Array com as linhas que serão adicionadas
 * @param {Array} novaLinha - Nova linha a ser verificada
 * @return {boolean} - true se a linha já existe, false caso contrário
 */
function verificarDuplicatasNoVetor(rowsToAdd, novaLinha) {
  // Percorre todas as linhas no array
  for (var i = 0; i < rowsToAdd.length; i++) {
    var linhaExistente = rowsToAdd[i];

    // Compara todos os campos da linha
    if (
      linhaExistente[0] === novaLinha[0] && // Email
      linhaExistente[1] === novaLinha[1] && // Pergunta
      linhaExistente[2] === novaLinha[2] && // Resposta
      linhaExistente[3] === novaLinha[3] && // Maturidade
      linhaExistente[4] === novaLinha[4] && // Recomendação
      linhaExistente[5] === novaLinha[5]    // Plano de Ação
    ) {
      return true; // Linha duplicada encontrada
    }
  }
  return false; // Linha não é duplicata
}