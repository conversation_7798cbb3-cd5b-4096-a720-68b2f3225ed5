function copiarUltimaLinha() {
  var ss = SpreadsheetApp.openById('1omekRbbZBbE0w5OcERRbkNCAb1-1chyvQGbyQEGBbQk');
  var sheet = ss.getSheetByName('Fórmulas - somente atualizar');

  var lastRow = sheet.getLastRow();
  var lastCol = sheet.getLastColumn();
  var lastRowFormulas = sheet.getRange(lastRow, 1, 1, lastCol).getFormulas(); 


  for (var col = 0; col < lastRowFormulas[0].length; col++) {
    for (var row = 0; row < lastRowFormulas.length; row++) {
      var formula = lastRowFormulas[row][col];
      if (formula !== "") {
        var adjustedFormula = formula.replace(/\$?([A-Z]+)(\d+)/g, function(match, p1, p2) {
          if (match.startsWith("$") || p1 === "0") {
            return match;
          } else {
            var newRowIndex = parseInt(p2) + 1;
            return p1 + newRowIndex;
          }
        });
        lastRowFormulas[row][col] = adjustedFormula;
      }
    }
  }

  sheet.getRange(lastRow + 1, 1, 1, lastCol).setFormulas(lastRowFormulas);
}

function automatizarAnalise() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var respostasSheet = ss.getSheetByName('Respostas');
  var analiseSheet = ss.getSheetByName('Análise');
  var automatizacaoSheet = ss.getSheetByName('Automatização da análise');
  var respostasData = respostasSheet.getDataRange().getValues();
  var analiseData = analiseSheet.getDataRange().getValues();
  var automatizacaoData = automatizacaoSheet.getDataRange().getValues(); 

  var lPerguntas = respostasData[1];
  var rowsToAdd = []; 

  arrumarEstruturaVetores(analiseData); 

  for (var i = 2; i < respostasData.length; i++) {
    var email = respostasData[i][1];

    for (var j = 2; j < (respostasData[i].length - 4); j++) {
      var pergunta = lPerguntas[j];
      var respostaCliente = respostasData[i][j];
      var locLinha = achaLinha(analiseData, pergunta, respostaCliente);
      var maturidade, recomendacao, planoAcao;

      if (locLinha === "N/A") {
        maturidade = "N/A";
        recomendacao = "N/A";
        planoAcao = "N/A";
      } else {
        maturidade = analiseData[locLinha][3];
        recomendacao = analiseData[locLinha][4];
        planoAcao = analiseData[locLinha][5];
      }

      var novaLinha = [email, pergunta, respostaCliente, maturidade, recomendacao, planoAcao];

      var linhaExistente = automatizacaoData.find(function (row) {
        return (
          row[0] === novaLinha[0] &&
          row[1] === novaLinha[1] &&
          row[2] === novaLinha[2] &&
          row[3] === novaLinha[3] &&
          row[4] === novaLinha[4] &&
          row[5] === novaLinha[5]
        );
      });

      var linhaExistenteNoVetor = verificarDuplicatasNoVetor(rowsToAdd, novaLinha);

      if (!linhaExistente && !linhaExistenteNoVetor) {
        rowsToAdd.push(novaLinha);
      }
    }
  }

  if (rowsToAdd.length > 0) {
    automatizacaoSheet.getRange(automatizacaoSheet.getLastRow() + 1, 1, rowsToAdd.length, 6).setValues(rowsToAdd);
    var ui = SpreadsheetApp.getUi();
    ui.alert('Script Finalizado', 'Transcrição Finalizada com Sucesso, novas linhas adicionadas!', ui.ButtonSet.OK);
  } else {
    var ui = SpreadsheetApp.getUi();
    ui.alert('Nenhuma nova resposta', 'Não foi possível acrescentar pois não existem novas respostas ou somente respostas duplicadas!', ui.ButtonSet.OK);
  }
}

function achaLinha(array, perg, resp) {
  for (let y = 1; y < array.length; y++) {
    if (array[y][1].trim() === perg.trim() && array[y][2].trim() === resp.trim()) {
      return y;
    } else if (resp === "Não se aplica") {
      return "N/A";
    }
  }
  return "N/A";
}

function verificarDuplicatas(rowsToAdd, novaLinha) { 
  for (var i = 0; i < rowsToAdd.length; i++) {
    var linhaExistente = rowsToAdd[i];
    if (
      linhaExistente[0] === novaLinha[0] &&
      linhaExistente[1] === novaLinha[1] &&
      linhaExistente[2] === novaLinha[2] &&
      linhaExistente[3] === novaLinha[3] &&
      linhaExistente[4] === novaLinha[4] &&
      linhaExistente[5] === novaLinha[5]
    ) {
      return true; 
    }
  }
  return false;
}

function arrumarEstruturaVetores(analiseData) {
  var modalidadeP, perguntaP;

  for (var cl = 1; cl < analiseData.length; cl++) {
    var clData = analiseData[cl];
    if (clData[1] !== "" && clData[0] !== "") {
      modalidadeP = clData[0];
      perguntaP = clData[1];
    } else if (clData[0] === "" && clData[1] !== "") {
      perguntaP = clData[1];
      clData[0] = modalidadeP;
    } else if (clData[1] === "") {
      clData[1] = perguntaP;
      clData[0] = modalidadeP;
    }
  }
}

function verificarDuplicatasNoVetor(rowsToAdd, novaLinha) {
  for (var i = 0; i < rowsToAdd.length; i++) {
    var linhaExistente = rowsToAdd[i];
    if (
      linhaExistente[0] === novaLinha[0] &&
      linhaExistente[1] === novaLinha[1] &&
      linhaExistente[2] === novaLinha[2] &&
      linhaExistente[3] === novaLinha[3] &&
      linhaExistente[4] === novaLinha[4] &&
      linhaExistente[5] === novaLinha[5]
    ) {
      return true; 
    }
  }
  return false; 
}