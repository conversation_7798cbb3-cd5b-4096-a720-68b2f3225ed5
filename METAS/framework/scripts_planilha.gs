/**
 * Analisa dados específicos baseados em email e data fornecidos pelo usuário
 * Limpa a planilha 'Automatização da análise' e inclui apenas os dados filtrados
 */
function analisarPorEmailEData() {
  var ui = SpreadsheetApp.getUi();

  // Solicita email e data ao usuário
  var emailResponse = ui.prompt('Filtro por Email', 'Digite o email que deseja analisar:', ui.ButtonSet.OK_CANCEL);
  if (emailResponse.getSelectedButton() !== ui.Button.OK) {
    return; // Usuário cancelou
  }
  var emailFiltro = emailResponse.getResponseText().trim();

  var dataResponse = ui.prompt('Filtro por Data', 'Digite a data que deseja analisar (formato: DD/MM/AAAA):', ui.ButtonSet.OK_CANCEL);
  if (dataResponse.getSelectedButton() !== ui.Button.OK) {
    return; // Usuário cancelou
  }
  var dataFiltro = dataResponse.getResponseText().trim();

  // Valida se os campos foram preenchidos
  if (!emailFiltro || !dataFiltro) {
    ui.alert('Erro', 'Email e data são obrigatórios!', ui.ButtonSet.OK);
    return;
  }

  // Executa a análise filtrada
  executarAnaliseFiltrада(emailFiltro, dataFiltro);
}

/**
 * Executa a análise filtrada por email e data específicos
 * @param {string} emailFiltro - Email para filtrar os dados
 * @param {string} dataFiltro - Data para filtrar os dados (formato DD/MM/AAAA)
 */
function executarAnaliseFiltrада(emailFiltro, dataFiltro) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var ui = SpreadsheetApp.getUi();

  // Obtém referências para as abas da planilha
  var respostasSheet = ss.getSheetByName('Respostas');
  var analiseSheet = ss.getSheetByName('Análise');
  var automatizacaoSheet = ss.getSheetByName('Automatização da análise');

  // Carrega todos os dados das abas em arrays
  var respostasData = respostasSheet.getDataRange().getValues();
  var analiseData = analiseSheet.getDataRange().getValues();

  // Obtém a linha de cabeçalho com as perguntas (linha 0, índice 0)
  var lPerguntas = respostasData[0];
  var rowsToAdd = []; // Array para armazenar linhas filtradas

  // Corrige a estrutura dos dados de análise
  arrumarEstruturaVetores(analiseData);

  // Converte a data do filtro para objeto Date para comparação
  var dataFiltroObj = converterStringParaData(dataFiltro);
  if (!dataFiltroObj) {
    ui.alert('Erro', 'Formato de data inválido! Use DD/MM/AAAA', ui.ButtonSet.OK);
    return;
  }

  var dadosEncontrados = false;

  // Log para debug - mostra filtros aplicados
  console.log(`=== FILTROS APLICADOS ===`);
  console.log(`Email filtro: "${emailFiltro}"`);
  console.log(`Data filtro: "${dataFiltro}"`);
  console.log(`Data filtro objeto:`, dataFiltroObj);

  // Percorre todas as respostas (começando da linha 2, índice 1)
  for (var i = 1; i < respostasData.length; i++) {
    var email = respostasData[i][1]; // Email do respondente (coluna B)
    var dataResposta = respostasData[i][0]; // Data da resposta (coluna A)

    // Log para debug - mostra dados da linha atual
    console.log(`\n--- Verificando linha ${i} ---`);
    console.log(`Email na planilha: "${email}"`);
    console.log(`Data na planilha: "${dataResposta}" (tipo: ${typeof dataResposta})`);

    // Verifica se o email corresponde ao filtro
    if (!email || email.toString().trim().toLowerCase() !== emailFiltro.toLowerCase()) {
      console.log(`Email não corresponde. Esperado: "${emailFiltro}", Encontrado: "${email}"`);
      continue; // Pula para a próxima linha se email não corresponde
    }

    // Verifica se a data corresponde ao filtro
    var dataRespostaObj;
    if (dataResposta instanceof Date) {
      dataRespostaObj = dataResposta;
    } else {
      dataRespostaObj = new Date(dataResposta);
    }

    console.log(`Data resposta objeto:`, dataRespostaObj);
    console.log(`Comparação de datas:`, compararDatas(dataRespostaObj, dataFiltroObj));

    if (!compararDatas(dataRespostaObj, dataFiltroObj)) {
      console.log(`Data não corresponde.`);
      console.log(`  Data filtro: ${dataFiltroObj.getDate()}/${dataFiltroObj.getMonth() + 1}/${dataFiltroObj.getFullYear()}`);
      console.log(`  Data resposta: ${dataRespostaObj.getDate()}/${dataRespostaObj.getMonth() + 1}/${dataRespostaObj.getFullYear()}`);
      continue; // Pula para a próxima linha se data não corresponde
    }

    dadosEncontrados = true;
    console.log(`✅ CORRESPONDÊNCIA ENCONTRADA na linha ${i}!`);

    // Percorre todas as perguntas (começando da coluna 2, que são as perguntas do formulário)
    for (var j = 2; j < respostasData[i].length; j++) {
      var pergunta = lPerguntas[j]; // Nome da pergunta do cabeçalho
      var respostaCliente = respostasData[i][j]; // Resposta do cliente

      // Busca a linha correspondente na aba de análise
      var locLinha = achaLinha(analiseData, pergunta, respostaCliente);
      var maturidade, recomendacao, planoAcao;

      // Log para debug (remover em produção)
      if (locLinha === "N/A") {
        console.log(`Não encontrou correspondência para:`);
        console.log(`  Pergunta: "${pergunta}"`);
        console.log(`  Resposta: "${respostaCliente}"`);
      }

      // Se não encontrou correspondência, marca como N/A
      if (locLinha === "N/A") {
        maturidade = "N/A";
        recomendacao = "N/A";
        planoAcao = "N/A";
      } else {
        // Extrai os dados da linha encontrada (ajustado conforme estrutura real)
        maturidade = analiseData[locLinha][3] || "N/A";        // Coluna 3: Classificação
        recomendacao = analiseData[locLinha][4] || "N/A";      // Coluna 4: Análise da maturidade
        planoAcao = analiseData[locLinha][5] || "N/A";         // Coluna 5: Recomendações

        // Log para debug (remover em produção)
        console.log(`Encontrou correspondência na linha ${locLinha}:`);
        console.log(`  Pergunta: "${pergunta}"`);
        console.log(`  Resposta: "${respostaCliente}"`);
        console.log(`  Classificação: "${maturidade}"`);
        console.log(`  Análise: "${recomendacao}"`);
        console.log(`  Recomendações: "${planoAcao}"`);
      }

      // Cria uma nova linha com todos os dados
      // Estrutura: [Email, Pergunta, Resposta, Classificação, Análise da Maturidade, Recomendações]
      var novaLinha = [email, pergunta, respostaCliente, maturidade, recomendacao, planoAcao];
      rowsToAdd.push(novaLinha);
    }
  }

  // Limpa a planilha de automatização (mantém apenas o cabeçalho)
  limparPlanilhaAutomatizacao(automatizacaoSheet);

  // Se encontrou dados para o filtro especificado
  if (dadosEncontrados && rowsToAdd.length > 0) {
    // Adiciona as linhas filtradas na aba de automatização
    automatizacaoSheet.getRange(2, 1, rowsToAdd.length, 6).setValues(rowsToAdd);
    ui.alert('Análise Concluída',
             `Análise filtrada concluída com sucesso!\nEmail: ${emailFiltro}\nData: ${dataFiltro}\nLinhas processadas: ${rowsToAdd.length}`,
             ui.ButtonSet.OK);
  } else {
    // Busca emails e datas disponíveis para sugestão
    var emailsDisponiveis = [];
    var datasDisponiveis = [];

    for (var i = 2; i < respostasData.length; i++) {
      if (respostasData[i]) {
        var emailDisp = respostasData[i][1] ? respostasData[i][1].toString().trim() : "";
        var dataDisp = respostasData[i][0];

        if (emailDisp && emailsDisponiveis.indexOf(emailDisp) === -1) {
          emailsDisponiveis.push(emailDisp);
        }

        if (dataDisp) {
          var dataFormatadaDisp = dataDisp instanceof Date ? formatarData(dataDisp) : dataDisp.toString();
          if (dataFormatadaDisp && datasDisponiveis.indexOf(dataFormatadaDisp) === -1) {
            datasDisponiveis.push(dataFormatadaDisp);
          }
        }
      }
    }

    var mensagem = `Não foram encontrados dados para:\nEmail: ${emailFiltro}\nData: ${dataFiltro}\n\n`;
    mensagem += `Emails disponíveis:\n${emailsDisponiveis.slice(0, 5).join('\n')}\n\n`;
    mensagem += `Datas disponíveis:\n${datasDisponiveis.slice(0, 5).join('\n')}`;

    ui.alert('Nenhum Dado Encontrado', mensagem, ui.ButtonSet.OK);
  }
}

/**
 * Limpa todos os dados da planilha 'Automatização da análise', mantendo apenas o cabeçalho
 * @param {Sheet} automatizacaoSheet - Referência para a aba de automatização
 */
function limparPlanilhaAutomatizacao(automatizacaoSheet) {
  var lastRow = automatizacaoSheet.getLastRow();
  var lastCol = automatizacaoSheet.getLastColumn();

  // Se há dados além do cabeçalho (linha 1), limpa tudo a partir da linha 2
  if (lastRow > 1) {
    automatizacaoSheet.getRange(2, 1, lastRow - 1, lastCol).clearContent();
  }
}

/**
 * Converte uma string de data no formato DD/MM/AAAA para objeto Date
 * @param {string} dataString - String da data no formato DD/MM/AAAA
 * @return {Date|null} - Objeto Date ou null se formato inválido
 */
function converterStringParaData(dataString) {
  try {
    // Divide a string da data em partes
    var partes = dataString.split('/');
    if (partes.length !== 3) {
      return null;
    }

    var dia = parseInt(partes[0]);
    var mes = parseInt(partes[1]) - 1; // Mês em JavaScript é 0-indexado
    var ano = parseInt(partes[2]);

    // Valida se os valores são números válidos
    if (isNaN(dia) || isNaN(mes) || isNaN(ano)) {
      return null;
    }

    // Cria e retorna o objeto Date
    var data = new Date(ano, mes, dia);

    // Verifica se a data criada é válida
    if (data.getDate() !== dia || data.getMonth() !== mes || data.getFullYear() !== ano) {
      return null;
    }

    return data;
  } catch (error) {
    return null;
  }
}

/**
 * Compara duas datas para verificar se são do mesmo dia
 * @param {Date} data1 - Primeira data para comparação
 * @param {Date} data2 - Segunda data para comparação
 * @return {boolean} - true se as datas são do mesmo dia, false caso contrário
 */
function compararDatas(data1, data2) {
  if (!data1 || !data2) {
    return false;
  }

  return data1.getDate() === data2.getDate() &&
         data1.getMonth() === data2.getMonth() &&
         data1.getFullYear() === data2.getFullYear();
}

/**
 * Função alternativa que permite especificar email e data diretamente como parâmetros
 * Útil para testes ou chamadas programáticas
 * @param {string} email - Email para filtrar
 * @param {string} data - Data para filtrar (formato DD/MM/AAAA)
 */
function analisarPorEmailEDataDireto(email, data) {
  if (!email || !data) {
    var ui = SpreadsheetApp.getUi();
    ui.alert('Erro', 'Email e data são obrigatórios!', ui.ButtonSet.OK);
    return;
  }

  executarAnaliseFiltrада(email, data);
}

/**
 * Busca uma linha específica no array de análise baseada na resposta
 * @param {Array} array - Array bidimensional com os dados de análise
 * @param {string} perg - Pergunta a ser procurada (não utilizada na busca atual)
 * @param {string} resp - Resposta a ser procurada
 * @return {number|string} - Índice da linha encontrada ou "N/A" se não encontrada
 */
function achaLinha(array, perg, resp) {
  // Tratamento especial para respostas "Não se aplica"
  if (resp === "Não se aplica") {
    return "N/A";
  }

  var respostaBusca = resp ? resp.toString().trim() : "";

  // Se a resposta está vazia, retorna N/A
  if (respostaBusca === "") {
    return "N/A";
  }

  // Busca pela resposta exata na coluna 2
  for (let y = 1; y < array.length; y++) {
    // Verifica se a linha tem dados suficientes
    if (!array[y] || array[y].length < 6) {
      continue;
    }

    var respostaAnalise = array[y][2] ? array[y][2].toString().trim() : "";

    // Compara apenas a resposta (já que muitas linhas têm pergunta vazia)
    if (respostaAnalise === respostaBusca) {
      console.log(`Correspondência encontrada na linha ${y}:`);
      console.log(`  Resposta procurada: "${respostaBusca}"`);
      console.log(`  Resposta encontrada: "${respostaAnalise}"`);
      console.log(`  Classificação: "${array[y][3]}"`);
      return y; // Retorna o índice da linha encontrada
    }
  }

  // Log para debug quando não encontra
  console.log(`Não encontrou correspondência para a resposta: "${respostaBusca}"`);
  console.log("Respostas disponíveis na aba Análise:");
  for (let y = 1; y < Math.min(10, array.length); y++) {
    if (array[y] && array[y][2]) {
      console.log(`  Linha ${y}: "${array[y][2].toString().trim()}"`);
    }
  }

  return "N/A"; // Retorna N/A se não encontrou correspondência
}

/**
 * Corrige a estrutura dos dados de análise preenchendo campos vazios
 * com valores das linhas anteriores (modalidade e pergunta)
 * @param {Array} analiseData - Array bidimensional com os dados de análise
 */
function arrumarEstruturaVetores(analiseData) {
  var modalidadeP, perguntaP; // Variáveis para armazenar valores anteriores

  // Percorre todas as linhas dos dados de análise
  for (var cl = 1; cl < analiseData.length; cl++) {
    var clData = analiseData[cl];

    // Se ambos modalidade e pergunta estão preenchidos, atualiza as variáveis
    if (clData[1] !== "" && clData[0] !== "") {
      modalidadeP = clData[0];
      perguntaP = clData[1];
    }
    // Se modalidade está vazia mas pergunta está preenchida
    else if (clData[0] === "" && clData[1] !== "") {
      perguntaP = clData[1];
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
    // Se pergunta está vazia
    else if (clData[1] === "") {
      clData[1] = perguntaP;   // Preenche pergunta com valor anterior
      clData[0] = modalidadeP; // Preenche modalidade com valor anterior
    }
  }
}

/**
 * Função de debug para verificar a estrutura dos dados da aba 'Análise'
 * Execute esta função para entender como os dados estão organizados
 */
function debugEstruturaDados() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var analiseSheet = ss.getSheetByName('Análise');
  var analiseData = analiseSheet.getDataRange().getValues();

  console.log("=== DEBUG: Estrutura da aba 'Análise' ===");
  console.log("Total de linhas:", analiseData.length);
  console.log("Total de colunas:", analiseData[0] ? analiseData[0].length : 0);

  // Mostra as primeiras 5 linhas para análise
  for (var i = 0; i < Math.min(5, analiseData.length); i++) {
    console.log(`Linha ${i}:`, analiseData[i]);
  }

  // Mostra especificamente as colunas que deveriam conter pergunta e resposta
  console.log("\n=== Análise das colunas de pergunta e resposta ===");
  for (var i = 1; i < Math.min(10, analiseData.length); i++) {
    if (analiseData[i]) {
      console.log(`Linha ${i}:`);
      console.log(`  Coluna 0 (Modalidade): "${analiseData[i][0]}"`);
      console.log(`  Coluna 1 (Pergunta): "${analiseData[i][1]}"`);
      console.log(`  Coluna 2 (Resposta): "${analiseData[i][2]}"`);
      console.log(`  Coluna 3 (Maturidade): "${analiseData[i][3]}"`);
      console.log(`  Coluna 4 (Recomendação): "${analiseData[i][4]}"`);
      console.log(`  Coluna 5 (Plano Ação): "${analiseData[i][5]}"`);
      console.log("---");
    }
  }

  var ui = SpreadsheetApp.getUi();
  ui.alert('Debug Concluído', 'Verifique o console (Ctrl+Shift+I) para ver os dados da estrutura.', ui.ButtonSet.OK);
}

/**
 * Função de debug para verificar a estrutura dos dados da aba 'Respostas'
 * Execute esta função para entender como os dados estão organizados
 */
function debugEstruturaRespostas() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var respostasSheet = ss.getSheetByName('Respostas');
  var respostasData = respostasSheet.getDataRange().getValues();

  console.log("=== DEBUG: Estrutura da aba 'Respostas' ===");
  console.log("Total de linhas:", respostasData.length);
  console.log("Total de colunas:", respostasData[0] ? respostasData[0].length : 0);

  // Mostra o cabeçalho
  console.log("Cabeçalho (linha 0):", respostasData[0]);
  if (respostasData.length > 1) {
    console.log("Linha de perguntas (linha 1):", respostasData[1]);
  }

  // Mostra as primeiras 5 linhas de dados (começando da linha 1)
  console.log("\n=== Primeiras linhas de dados ===");
  for (var i = 1; i < Math.min(6, respostasData.length); i++) {
    if (respostasData[i]) {
      console.log(`Linha ${i}:`);
      console.log(`  Coluna 0 (Data): "${respostasData[i][0]}" - Tipo: ${typeof respostasData[i][0]}`);
      console.log(`  Coluna 1 (Email): "${respostasData[i][1]}"`);
      console.log(`  Primeiras 3 respostas: [${respostasData[i][2]}, ${respostasData[i][3]}, ${respostasData[i][4]}]`);
      console.log("---");
    }
  }

  // Mostra todos os emails únicos encontrados
  console.log("\n=== Emails encontrados na planilha ===");
  var emailsEncontrados = [];
  for (var i = 1; i < respostasData.length; i++) {
    if (respostasData[i] && respostasData[i][1]) {
      var email = respostasData[i][1].toString().trim();
      if (email && emailsEncontrados.indexOf(email) === -1) {
        emailsEncontrados.push(email);
      }
    }
  }
  emailsEncontrados.forEach(function(email, index) {
    console.log(`${index + 1}. "${email}"`);
  });

  // Mostra todas as datas únicas encontradas
  console.log("\n=== Datas encontradas na planilha ===");
  var datasEncontradas = [];
  for (var i = 1; i < respostasData.length; i++) {
    if (respostasData[i] && respostasData[i][0]) {
      var data = respostasData[i][0];
      var dataFormatada;

      if (data instanceof Date) {
        dataFormatada = formatarData(data);
      } else {
        dataFormatada = data.toString();
      }

      if (dataFormatada && datasEncontradas.indexOf(dataFormatada) === -1) {
        datasEncontradas.push(dataFormatada);
      }
    }
  }
  datasEncontradas.forEach(function(data, index) {
    console.log(`${index + 1}. "${data}"`);
  });

  var ui = SpreadsheetApp.getUi();
  ui.alert('Debug Respostas Concluído', 'Verifique o console para ver emails e datas disponíveis.', ui.ButtonSet.OK);
}

/**
 * Formata uma data para o padrão DD/MM/AAAA
 * @param {Date} data - Data a ser formatada
 * @return {string} - Data formatada
 */
function formatarData(data) {
  if (!(data instanceof Date)) {
    return data.toString();
  }

  var dia = data.getDate().toString().padStart(2, '0');
  var mes = (data.getMonth() + 1).toString().padStart(2, '0');
  var ano = data.getFullYear();

  return `${dia}/${mes}/${ano}`;
}